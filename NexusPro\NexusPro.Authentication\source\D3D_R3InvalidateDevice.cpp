/*
 * D3D_R3InvalidateDevice.cpp - D3D R3 Device Invalidation
 * Original Function: ?D3D_R3InvalidateDevice@@YAJXZ
 * Address: 0x14050B040
 * 
 * Invalidates D3D R3 graphics device resources and performs cleanup.
 * This function handles comprehensive cleanup of graphics resources
 * including shaders, buffers, effects, and render targets.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Invalidate D3D R3 graphics device and cleanup resources
// Original function: ?D3D_R3InvalidateDevice@@YAJXZ
// Address: 0x14050B040
__int64 D3D_R3InvalidateDevice(void) {
    // Invalidate nature rendering system
    CN_InvalidateNature();
    
    // Release vertex shader resources
    ReleaseVertexShaderList();
    
    // Release blur vertex buffer
    ReleaseBlurVBuffer();
    
    // Release full screen effect resources
    ReleaseFullScreenEffect();
    
    // Release old render target if valid
    if (stOldRenderTarget) {
        ((void(*)(void))stOldRenderTarget->vfptr->Release)();
        stOldRenderTarget = 0LL;
    }
    
    // Release old stencil Z buffer if valid
    if (stOldStencilZ) {
        ((void(*)(void))stOldStencilZ->vfptr->Release)();
        stOldStencilZ = 0LL;
    }
    
    return 0LL;
}
