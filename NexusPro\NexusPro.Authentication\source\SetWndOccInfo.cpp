/*
 * SetWndOccInfo.cpp - Window OCC Information Setting
 * Original Function: ?SetOccDialogInfo@CWnd@@HPEAU_AFX_OCC_DIALOG_INFO@@@Z_0
 * Address: 0x1404DBE1A
 * 
 * Sets OLE Control Container (OCC) dialog information for CWnd class.
 * This function is part of the MFC window management system for configuring
 * OLE controls embedded within window dialogs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Set OCC dialog information for CWnd class
// Parameters:
//   this - Pointer to CWnd instance
//   pOccInfo - Pointer to AFX_OCC_DIALOG_INFO structure containing OLE control configuration
// Returns: Integer result code (typically success/failure status)
int CWnd::SetOccDialogInfo(CWnd* this, struct _AFX_OCC_DIALOG_INFO* pOccInfo) {
    return CWnd::SetOccDialogInfo(this, pOccInfo);
}
