/*
 * AsyncLogVectorSize.cpp - Async Log Vector Size
 * Original Function: ?size@?$vector@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@QEBA_KXZ
 * Address: 0x1403C4210
 * 
 * Returns the size of vector containing async log list iterators.
 * This function calculates the number of elements in the vector of
 * list iterators used for async log management.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>
#include <iterator>
#include <list>
#include <utility>

// Get size of async log vector
// Original function: size@vector@_Iterator@list@pair...
// Address: 0x1403C4210
__int64 AsyncLogVector_size(std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>* this) {
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-18h]@1
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>* v5; // [sp+20h] [bp+8h]@1

    v5 = this;
    v1 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 4LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }
    
    // Calculate vector size based on pointer difference
    if (v5->_Myfirst) {
        v4 = (unsigned int)((char*)v5->_Mylast - (char*)v5->_Myfirst) / 24LL;
    }
    else {
        v4 = 0LL;
    }
    
    return v4;
}
