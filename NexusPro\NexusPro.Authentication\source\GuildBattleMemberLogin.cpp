/*
 * GuildBattleMemberLogin.cpp - Guild Battle Member <PERSON><PERSON> Handler
 * Original Function: ?Login@CNormalGuildBattleGuildMember@GUILD_BATTLE@@XXZ
 * Address: 0x1403DFA80
 * 
 * Handles login process for individual guild battle members.
 * This function manages member authentication and PvP point retrieval
 * during guild battle member login.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Handle guild battle member login process
// Original function: ?Login@CNormalGuildBattleGuildMember@GUILD_BATTLE@@XXZ
// Address: 0x1403DFA80
void GUILD_BATTLE::CNormalGuildBattleGuildMember::Login(GUILD_BATTLE::CNormalGuildBattleGuildMember* this, long double a2) {
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    GUILD_BATTLE::CNormalGuildBattleGuildMember* v5; // [sp+30h] [bp+8h]@1

    v5 = this;
    v2 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Get PvP point from player database
    CPlayerDB::GetPvPPoint(&v5->m_pkMember->pPlayer->m_Param);
    
    // Set PvP point value
    v5->m_dPvpPoint = a2;
}
