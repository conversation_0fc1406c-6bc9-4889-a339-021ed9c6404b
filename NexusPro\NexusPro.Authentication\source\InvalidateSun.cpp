/*
 * InvalidateSun.cpp - Sun System Invalidation
 * Original Function: ?InvalidateSun@Sun@@XXZ
 * Address: 0x1405221E0
 * 
 * Invalidates sun system resources in the graphics engine.
 * This function handles cleanup of sun-related rendering resources
 * when the graphics device needs to be reset or invalidated.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Invalidate sun system resources
// Original function: ?InvalidateSun@Sun@@XXZ
// Address: 0x1405221E0
void Sun::InvalidateSun(Sun* this) {
    Sun* v1; // rbx@1
    __int64 v2; // rcx@1

    v1 = this;
    v2 = *((QWORD*)this + 5);
    
    // Release sun resource if valid
    if (v2) {
        (*(void(**)(void))(*(QWORD*)v2 + 16LL))();
    }
    
    // Clear sun resource reference
    *((QWORD*)v1 + 5) = 0LL;
}
