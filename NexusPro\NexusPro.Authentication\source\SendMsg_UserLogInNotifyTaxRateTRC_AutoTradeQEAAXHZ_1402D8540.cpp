/*
 * AutoTradeTaxRateNotify.cpp
 * Original Function: TRC_AutoTrade::SendMsg_UserLogInNotifyTaxRate
 * Original Address: 0x1402D8540
 *
 * Description: Sends tax rate notification message when user logs in to auto trade system.
 * Notifies the client about current tax rates for auto trading transactions.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Send tax rate notification on user login to auto trade system
void TRC_AutoTrade::SendMsg_UserLogInNotifyTaxRate(TRC_AutoTrade *this, int n) {
    __int64 *v2;                      // Stack pointer for initialization
    signed __int64 i;                 // Loop counter for stack initialization
    __int64 v4;                       // Stack buffer [sp+0h] [bp-78h]
    char szMsg;                       // Message data [sp+34h] [bp-44h]
    char pbyType;                     // Message type [sp+54h] [bp-24h]
    char v7;                          // Message subtype [sp+55h] [bp-23h]

    v2 = &v4;

    // Initialize stack buffer with debug pattern
    for (i = 28LL; i; --i) {
        *(DWORD *)v2 = 0xCCCCCCCC;   // Debug fill pattern
        v2 = (__int64 *)((char *)v2 + 4);
    }

    // Set up tax rate notification message
    szMsg = 1;      // Tax rate enabled flag
    pbyType = 30;   // Auto trade message type
    v7 = 39;        // Tax rate notification subtype

    // Send the tax rate notification message
    CNetProcess::LoadSendMsg(unk_1414F2088, n, &pbyType, &szMsg, 1);
}


