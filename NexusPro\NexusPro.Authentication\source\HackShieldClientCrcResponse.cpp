/*
 * HackShieldClientCrcResponse.cpp - HackShield Client CRC Response Handler
 * Original Function: ?OnRecvSession_ClientCrc_Response@HACKSHEILD_PARAM_ANTICP@@_N_KPEAD@Z
 * Address: 0x140418290
 * 
 * Handles client CRC response for HackShield anti-CPU parameter system.
 * This function processes client-side CRC validation responses and
 * manages the final verification state for anti-cheat protection.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Handle client CRC response for HackShield anti-CPU parameter
// Original function: OnRecvSession_ClientCrc_Response@HACKSHEILD_PARAM_ANTICP...
// Address: 0x140418290
char HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCrc_Response(HACKSHEILD_PARAM_ANTICP* this, unsigned __int64 tSize, char* pMsg) {
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    char result; // al@5
    __int64 v6; // [sp+0h] [bp-48h]@1
    char* v7; // [sp+20h] [bp-28h]@11
    unsigned int dwRet; // [sp+28h] [bp-20h]@11
    _HSHIELD_CLIENT_CONTEXT* v9; // [sp+30h] [bp-18h]@11
    HACKSHEILD_PARAM_ANTICP* v10; // [sp+50h] [bp+8h]@1

    v10 = this;
    v3 = &v6;
    
    // Initialize memory with debug pattern
    for (i = 16LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    // Check if message size is correct (74 bytes)
    if (tSize == 74) {
        // Validate socket index range
        if (v10->m_nSocketIndex >= 0 && v10->m_nSocketIndex < 2532) {
            // Check if in correct verification state (state 3)
            if (v10->m_byVerifyState == 3) {
                v7 = pMsg;
                v9 = &v10->m_CrcInfo;
                
                // Analyze CRC acknowledgment message
                dwRet = _AntiCpSvr_AnalyzeAckMsg(&v10->m_CrcInfo, pMsg + 2, v10->m_byGUIDClientInfo);
                
                if (dwRet) {
                    // CRC analysis failed, kick user
                    HACKSHEILD_PARAM_ANTICP::Kick(v10, 3, dwRet);
                    result = 1;
                }
                else {
                    // CRC analysis successful, move to final verification state
                    v10->m_byVerifyState = 4;
                    result = 1;
                }
            }
            else {
                // Wrong verification state, kick user
                HACKSHEILD_PARAM_ANTICP::Kick(v10, 3, 0xFFFFFFFF);
                result = 1;
            }
        }
        else {
            result = 0;
        }
    }
    else {
        result = 0;
    }
    return result;
}
