/*
 * AsyncLogListNodeAllocator.cpp - Async Log List Node Allocator
 * Original Function: ?allocate@?$allocator@U_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@PEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@_K@Z
 * Address: 0x1403C7000
 * 
 * Allocates memory for list nodes in the async log system.
 * This function handles memory allocation for STL list nodes containing
 * pairs of integers and CAsyncLogInfo pointers.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <memory>
#include <utility>

// Allocate memory for async log list nodes
// Original function: allocate@allocator@U_Node@_List_nod...
// Address: 0x1403C7000
void* AsyncLogListNodeAllocator_allocate(void* this, unsigned __int64 _Count) {
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1

    v2 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Allocate memory for list nodes containing async log info pairs
    return std::_Allocate<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node>(
        _Count,
        0LL);
}
