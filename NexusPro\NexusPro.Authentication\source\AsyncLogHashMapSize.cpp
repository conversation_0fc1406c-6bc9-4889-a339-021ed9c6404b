/*
 * AsyncLogHashMapSize.cpp - Async Log Hash Map Size Operation
 * Original Function: ?size@?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@QEBA_KXZ
 * Address: 0x1403C3080
 * 
 * Returns the number of elements in the async log hash map.
 * This function provides the current size of the hash map containing
 * async log information key-value pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <list>
#include <memory>
#include <utility>
#include <hash_map>

// Get size of async log hash map
// Original function: size@_Hash@_Hmap_traits@int@CAsyncLogInfo...
// Address: 0x1403C3080
unsigned __int64 AsyncLogHashMap_size(stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* this) {
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* v5; // [sp+30h] [bp+8h]@1

    v5 = this;
    v1 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }
    
    // Return size of underlying list
    return std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::size(&v5->_List);
}
