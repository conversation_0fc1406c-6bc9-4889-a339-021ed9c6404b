/*
 * AsyncLogCopyOpt.cpp - Async Log Copy Optimization
 * Original Function: ??$_Copy_opt@PEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@PEAV123@Urandom_access_iterator_tag@3@@std@@YAPEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@0@PEAV120@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403C8530
 * 
 * Optimized copy operation for async log list iterators.
 * This function performs efficient copying of iterator ranges using
 * random access iterator optimization for better performance.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>

// Optimized copy operation for async log iterators
// Original function: _Copy_opt@_Iterator@list@pair...
// Address: 0x1403C8530
std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* AsyncLog_Copy_opt(
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _First,
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _Last,
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _Dest,
    std::random_access_iterator_tag __formal,
    std::_Nonscalar_ptr_iterator_tag a5,
    std::_Range_checked_iterator_tag a6) {
    
    __int64* v6; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v9; // [sp+0h] [bp-28h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* __that; // [sp+30h] [bp+8h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* v11; // [sp+38h] [bp+10h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* v12; // [sp+40h] [bp+18h]@1

    v12 = _Dest;
    v11 = _Last;
    __that = _First;
    v6 = &v9;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v6 = 0xCCCCCCCC;
        v6 = (__int64*)((char*)v6 + 4);
    }
    
    // Copy elements from source range to destination
    while (__that != v11) {
        std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>::operator=(v12, __that);
        ++v12;
        ++__that;
    }
    
    return v12;
}
