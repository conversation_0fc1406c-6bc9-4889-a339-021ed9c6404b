/*
 * AsyncLogVectorCapacity.cpp - Async Log Vector Capacity Operation
 * Original Function: ?capacity@?$std::vector@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@QEBA_KXZ
 * Address: 0x1403C6920
 * 
 * Returns the capacity of the async log vector.
 * This function calculates the maximum number of elements that can be
 * stored in the vector without requiring reallocation.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>
#include <iterator>
#include <list>
#include <utility>

// Get capacity of async log vector
// Original function: capacity@vector@_Iterator@list@pair...
// Address: 0x1403C6920
__int64 AsyncLogVector_capacity(std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* this) {
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-18h]@1
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* v5; // [sp+20h] [bp+8h]@1

    v5 = this;
    v1 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 4LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }
    
    // Calculate capacity based on allocated memory
    if (v5->_Myfirst) {
        v4 = (unsigned int)((char*)v5->_Myend - (char*)v5->_Myfirst) / 24LL;
    }
    else {
        v4 = 0LL;
    }
    
    return v4;
}
