/*
 * AsyncLogVectorUmove.cpp - Async Log Vector Uninitialized Move
 * Original Function: ??$_Umove@PEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@?$std::vector@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@IEAAPEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@1@PEAV231@00@Z
 * Address: 0x1403C7A30
 * 
 * Performs uninitialized move operation for async log vector iterators.
 * This function moves elements from source range to uninitialized memory
 * using move semantics for better performance.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>
#include <iterator>
#include <list>
#include <memory>
#include <utility>

// Uninitialized move operation for async log vector
// Original function: _Umove@vector@_Iterator@list@pair...
// Address: 0x1403C7A30
std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* AsyncLogVector_Umove(
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>>>* this,
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _First,
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _Last,
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _Ptr) {
    
    __int64* v4; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v7; // [sp+0h] [bp-28h]@1
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>>>* v8; // [sp+30h] [bp+8h]@1

    v8 = this;
    v4 = &v7;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v4 = 0xCCCCCCCC;
        v4 = (__int64*)((char*)v4 + 4);
    }
    
    // Perform uninitialized move using allocator
    return stdext::_Unchecked_uninitialized_move<
        std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>*,
        std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>*,
        std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>>>(
        _First,
        _Last,
        _Ptr,
        &v8->_Alval);
}
