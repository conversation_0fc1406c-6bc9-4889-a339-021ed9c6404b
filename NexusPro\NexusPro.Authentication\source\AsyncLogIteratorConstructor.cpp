/*
 * AsyncLogIteratorConstructor.cpp - Async Log Iterator Constructor
 * Original Function: ??0?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@AEBV012@@Z
 * Address: 0x1403C2D20
 * 
 * Copy constructor for async log list iterator.
 * This function creates a new iterator by copying from an existing iterator,
 * maintaining proper container and node references.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <utility>

// Copy constructor for async log iterator
// Original function: constructor for _Iterator@list@pair...
// Address: 0x1403C2D20
void AsyncLogIterator_constructor(std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* this, const std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* __that) {
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* v5; // [sp+30h] [bp+8h]@1

    v5 = this;
    v2 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Copy container and node references from source iterator
    this->_Mycont = __that->_Mycont;
    this->_Mynode = __that->_Mynode;
}
