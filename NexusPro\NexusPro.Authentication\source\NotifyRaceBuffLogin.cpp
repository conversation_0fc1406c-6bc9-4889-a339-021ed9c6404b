/*
 * NotifyRaceBuffLogin.cpp - Race Buff Login Notification
 * Original Function: ?NotifyLogInSetBuff@CRaceBuffInfoByHolyQuest@@XG@Z
 * Address: 0x1403B42D0
 * 
 * Notifies user login and sets race buff information for holy quest system.
 * This function handles the communication of race-specific buff data when
 * a user logs into the game, ensuring proper buff state synchronization.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Notify login and set buff for race buff info by holy quest
// Original function: ?NotifyLogInSetBuff@CRaceBuffInfoByHolyQuest@@XG@Z
// Address: 0x1403B42D0
void CRaceBuffInfoByHolyQuest::NotifyLogInSetBuff(CRaceBuffInfoByHolyQuest* this, unsigned __int16 wUserInx) {
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-78h]@1
    char szMsg; // [sp+34h] [bp-44h]@4
    __int16 v6; // [sp+35h] [bp-43h]@4
    __int16 v7; // [sp+37h] [bp-41h]@4
    char pbyType; // [sp+54h] [bp-24h]@4
    char v9; // [sp+55h] [bp-23h]@4
    CRaceBuffInfoByHolyQuest* v10; // [sp+80h] [bp+8h]@1

    v10 = this;
    v2 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 28LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Prepare message data for buff notification
    szMsg = v10->m_byLv;
    v6 = v10->m_pData->m_dwIndex;
    v7 = 0;
    pbyType = 17;
    v9 = 25;
    
    // Send buff notification message to user
    CNetProcess::LoadSendMsg(unk_1414F2088, wUserInx, &pbyType, &szMsg, 5u);
}
