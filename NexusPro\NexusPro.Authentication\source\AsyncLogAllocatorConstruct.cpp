/*
 * AsyncLogAllocatorConstruct.cpp - Async Log Allocator Construct
 * Original Function: ?construct@?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@XPEAU?$std::pair@$$CBHPEAVCAsyncLogInfo@@@2@AEBU32@@Z
 * Address: 0x1403C6EA0
 * 
 * Constructs an object in-place using the async log allocator.
 * This function handles construction of pairs containing integers
 * and CAsyncLogInfo pointers in allocated memory.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <utility>

// Construct object in-place using async log allocator
// Original function: construct@allocator@pair...
// Address: 0x1403C6EA0
void AsyncLogAllocator_construct(void* this, void* _Ptr, void* _Val) {
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1

    v3 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    // Construct the object in-place
    std::_Construct<std::pair<int const, CAsyncLogInfo*>, std::pair<int const, CAsyncLogInfo*>>(_Ptr, _Val);
}
