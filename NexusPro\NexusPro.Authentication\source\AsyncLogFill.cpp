/*
 * AsyncLogFill.cpp - Async Log Fill Operation
 * Original Function: ??$_Fill@PEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V123@@std@@YAXPEAV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@0@0AEBV120@@Z
 * Address: 0x1403C8680
 * 
 * Fills a range of async log list iterators with a specified value.
 * This function assigns the same iterator value to all elements
 * in the specified range for initialization purposes.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>

// Fill range with async log iterator value
// Original function: _Fill@_Iterator@list@pair...
// Address: 0x1403C8680
void AsyncLog_Fill(
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _First,
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _Last,
    const std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* _Val) {
    
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* v6; // [sp+30h] [bp+8h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* v7; // [sp+38h] [bp+10h]@1
    const std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>* __that; // [sp+40h] [bp+18h]@1

    __that = _Val;
    v7 = _Last;
    v6 = _First;
    v3 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    // Fill range with the specified value
    while (v6 != v7) {
        std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Iterator<0>::operator=(v6, __that);
        ++v6;
    }
}
