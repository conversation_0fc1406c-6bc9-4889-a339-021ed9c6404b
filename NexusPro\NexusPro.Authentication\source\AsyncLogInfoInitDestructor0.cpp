/*
 * AsyncLogInfoInitDestructor0.cpp - Async Log Info Init Destructor 0
 * Original Function: _CAsyncLogInfo::Init_::_1_::dtor$0
 * Address: 0x1403BD0C0
 * 
 * Destructor function for CAsyncLogInfo initialization process.
 * This function handles cleanup of memory allocated during the
 * async log info initialization process.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Destructor for CAsyncLogInfo initialization process
// Original function: _CAsyncLogInfo::Init_::_1_::dtor$0
// Address: 0x1403BD0C0
void CAsyncLogInfo::Init_::_1_::dtor_0(__int64 a1, __int64 a2) {
    // Delete allocated memory at offset 10640
    operator delete(*(void**)(a2 + 10640));
}
