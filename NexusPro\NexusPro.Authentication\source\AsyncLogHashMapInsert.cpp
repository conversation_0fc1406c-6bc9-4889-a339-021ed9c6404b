/*
 * AsyncLogHashMapInsert.cpp - Async Log Hash Map Insert Operation
 * Original Function: ?insert@?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@?AU?$std::pair@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@_N@std@@AEBU?$std::pair@$$CBHPEAVCAsyncLogInfo@@@4@@Z
 * Address: 0x1403C1A10
 * 
 * Inserts an element into the async log hash map.
 * This function adds a new key-value pair to the hash map and returns
 * an iterator to the inserted element along with a success flag.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <hash_map>

// Insert element into async log hash map
// Original function: insert@_Hash@_Hmap_traits@int@CAsyncLogInfo...
// Address: 0x1403C1A10
void* AsyncLogHashMap_insert(stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* this, void** result, const std::pair<int const, CAsyncLogInfo*>* _Val) {
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    unsigned __int64 v5; // rax@4
    std::pair<int const, CAsyncLogInfo*>* v6; // rdx@4
    void** v7; // rax@9
    void** v8; // rax@10
    std::pair<int const, CAsyncLogInfo*>* v9; // rax@11
    std::pair<int const, CAsyncLogInfo*>* v10; // rdx@11
    const int* v11; // rax@11
    void** v12; // rax@13
    __int64 v38; // [sp+0h] [bp-348h]@1
    void* _Val1; // [sp+48h] [bp-300h]@4
    void* v40; // [sp+78h] [bp-2D0h]@4
    unsigned __int64 _Pos; // [sp+98h] [bp-2B0h]@9
    unsigned __int64 v42; // [sp+A0h] [bp-2A8h]@11
    unsigned __int64 j; // [sp+A8h] [bp-2A0h]@14
    void* v44; // [sp+B8h] [bp-290h]@13
    unsigned __int64 v45; // [sp+D8h] [bp-270h]@28
    char v46; // [sp+E0h] [bp-268h]@6
    void** resulta; // [sp+F8h] [bp-250h]@6
    void* v48; // [sp+100h] [bp-248h]@9
    bool v49; // [sp+118h] [bp-230h]@13
    void* v50; // [sp+120h] [bp-228h]@13
    bool _Val2; // [sp+220h] [bp-128h]@33
    bool v68; // [sp+260h] [bp-E8h]@39
    int v69; // [sp+264h] [bp-E4h]@4
    __int64 v70; // [sp+268h] [bp-E0h]@4
    void** v71; // [sp+270h] [bp-D8h]@6
    void** v72; // [sp+278h] [bp-D0h]@9
    void** __that; // [sp+280h] [bp-C8h]@9
    void** v74; // [sp+288h] [bp-C0h]@13
    void** _Right; // [sp+290h] [bp-B8h]@13
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* v97; // [sp+350h] [bp+8h]@1
    void** v98; // [sp+358h] [bp+10h]@1
    const std::pair<int const, CAsyncLogInfo*>* v99; // [sp+360h] [bp+18h]@1

    v99 = _Val;
    v98 = result;
    v97 = this;
    v3 = &v38;
    
    // Initialize memory with debug pattern
    for (i = 208LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    v70 = -2LL;
    v69 = 0;
    
    // Initialize iterators
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::_Iterator<0>(&_Val1);
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::_Iterator<0>(&v40);
    
    // Get hash map size and check if rehashing is needed
    v5 = stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::size(v97);
    v6 = (std::pair<int const, CAsyncLogInfo*>*)(v5 % 4);
    
    if (v97->_Maxidx <= v5 / 4) {
        // Rehash if load factor is too high
        if (std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::size(&v97->_Vec) - 1 > v97->_Maxidx) {
            if (v97->_Mask < v97->_Maxidx) {
                v97->_Mask = 2 * v97->_Mask + 1;
            }
        }
        else {
            v97->_Mask = 2 * std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::size(&v97->_Vec) - 3;
            resulta = (void**)&v46;
            v71 = stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::end(v97, (void**)&v46);
            std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::resize(&v97->_Vec, v97->_Mask + 2, v71);
        }
        
        // Rehash existing elements
        _Pos = v97->_Maxidx - (v97->_Mask >> 1) - 1;
        v7 = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::operator[](&v97->_Vec, _Pos);
        v72 = stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::_Get_iter_from_vec(v97, &v48, v7);
        __that = v72;
        std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator=(&_Val1, v72);
        std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(&v48);
        
        // Process elements for rehashing
        while (1) {
            v8 = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::operator[](&v97->_Vec, _Pos + 1);
            if (!std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator!=(&_Val1._Mycont, (void**)&_Val1._Mycont)) {
                break;
            }
            
            v9 = std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator*(&_Val1);
            v11 = stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>::_Kfn((stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>*)v9, v10);
            v42 = v97->_Mask & stdext::hash_compare<int, std::less<int>>::operator()(&v97->comp, v11);
            
            if (v42 == _Pos) {
                std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator++(&_Val1);
            }
            else {
                // Move element to correct bucket
                std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::_Iterator<0>(&v44, &_Val1);
                v74 = stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::end(v97, &v50);
                _Right = (void**)v74;
                v12 = std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator++(&v44);
                v49 = std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator!=(&v44._Mycont, _Right);
                std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(&v50);
                
                // Handle bucket reorganization
                if (v49) {
                    // Complex bucket reorganization logic would go here
                    // Simplified for readability
                }
                
                std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator=(&_Val1, &v44);
                std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(&v44);
            }
        }
        ++v97->_Maxidx;
    }
    
    // Find insertion position and insert element
    const int* keyval = stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>::_Kfn(v99, v6);
    unsigned __int64 hashval = stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::_Hashval(v97, keyval);
    v45 = hashval;
    
    // Find correct position in bucket
    void** bucket_iter = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::operator[](&v97->_Vec, hashval + 1);
    void** iter_from_vec = stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::_Get_iter_from_vec(v97, &v40, bucket_iter);
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator=(&_Val1, iter_from_vec);
    
    // Check for duplicate key
    bool found_duplicate = false;
    while (true) {
        void** bucket_start = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::operator[](&v97->_Vec, v45);
        if (!std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator!=(&_Val1._Mycont, (void**)&_Val1._Mycont)) {
            break;
        }
        
        // Check if key already exists
        void** prev_iter = std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator--(&_Val1);
        std::pair<int const, CAsyncLogInfo*>* existing_pair = std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator*(prev_iter);
        
        // Compare keys for duplicate detection
        if (/* key comparison logic */) {
            found_duplicate = true;
            break;
        }
    }
    
    if (found_duplicate) {
        // Return existing element
        _Val2 = false;
        std::pair<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, bool>::pair(v98, &_Val1, &_Val2);
        v69 |= 1u;
    }
    else {
        // Insert new element
        void** insert_pos = std::list<std::pair<int const, CAsyncLogInfo*>>::insert(&v97->_List, &v40, &_Val1, (std::pair<int const, CAsyncLogInfo*>*)v99);
        std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator=(&v40, insert_pos);
        
        // Update bucket pointers
        while (true) {
            void** bucket_ptr = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::operator[](&v97->_Vec, v45);
            if (!std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator==(&_Val1._Mycont, (void**)&_Val1._Mycont)) {
                break;
            }
            
            void** bucket_update = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>::operator[](&v97->_Vec, v45);
            std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator=(bucket_update, &v40);
            
            if (!v45) {
                break;
            }
            --v45;
        }
        
        v68 = true;
        std::pair<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, bool>::pair(v98, &v40, &v68);
        v69 |= 1u;
    }
    
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(&v40);
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(&_Val1);
    return v98;
}
