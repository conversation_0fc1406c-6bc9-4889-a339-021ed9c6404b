/*
 * GetWndOccInfo.cpp - Window OCC Information Retrieval
 * Original Function: ?GetOccDialogInfo@CWnd@@PEAU_AFX_OCC_DIALOG_INFO@@XZ_0
 * Address: 0x1404DBE20
 * 
 * Retrieves OLE Control Container (OCC) dialog information for CWnd class.
 * This function is part of the MFC window management system for handling
 * OLE controls embedded within window dialogs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Get OCC dialog information for CWnd class
// Returns pointer to AFX_OCC_DIALOG_INFO structure containing OLE control information
struct _AFX_OCC_DIALOG_INFO* CWnd::GetOccDialogInfo(CWnd* this) {
    return CWnd::GetOccDialogInfo(this);
}
