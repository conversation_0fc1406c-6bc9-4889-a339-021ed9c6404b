/*
 * HMACMessageAuthCodeDeletingDestructor.cpp - HMAC Message Authentication Code Deleting Destructor
 * Original Function: ??_E?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@PEAXI@Z
 * Address: 0x140465900
 * 
 * Deleting destructor for HMAC-based message authentication code implementation.
 * This function handles destruction and optional memory deallocation of
 * HMAC SHA1 message authentication code objects.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Deleting destructor for HMAC message authentication code
// Original function: MessageAuthenticationCodeImpl...vector deleting destructor
// Address: 0x140465900
CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>* 
HMACMessageAuthCode_deleting_destructor(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>* this, 
    int a2) {
    
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>* v6; // [sp+30h] [bp+8h]@1
    int v7; // [sp+38h] [bp+10h]@1

    v7 = a2;
    v6 = this;
    v2 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Call the destructor
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>::
        ~MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>(v6);
    
    // Delete memory if requested (bit 0 set)
    if (v7 & 1) {
        operator delete(v6);
    }
    
    return v6;
}
