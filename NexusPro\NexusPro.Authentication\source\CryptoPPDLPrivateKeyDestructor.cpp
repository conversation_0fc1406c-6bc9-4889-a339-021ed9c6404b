/*
 * CryptoPPDLPrivateKeyDestructor.cpp - CryptoPP DL Private Key Destructor
 * Original Function: _CryptoPP::DL_PrivateKeyImpl_CryptoPP::DL_GroupParameters_EC_CryptoPP::ECP___::Validate_::_1_::dtor$0
 * Address: 0x140451850
 * 
 * Destructor for CryptoPP DL private key implementation validation.
 * This function handles cleanup of cryptographic integer objects
 * during the destruction of elliptic curve private key validation.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Destructor for CryptoPP DL private key validation
// Original function: _CryptoPP::DL_PrivateKeyImpl...::dtor$0
// Address: 0x140451850
void CryptoPPDLPrivateKey_Validate_destructor(__int64 a1, __int64 a2) {
    // Check if integer object needs cleanup (bit 0 set)
    if (*((DWORD*)a2 + 104) & 1) {
        // Clear the flag
        *((DWORD*)a2 + 104) &= 0xFFFFFFFE;
        
        // Destroy the integer object at offset 64
        CryptoPP::Integer::~Integer((void*)(a2 + 64));
    }
}
