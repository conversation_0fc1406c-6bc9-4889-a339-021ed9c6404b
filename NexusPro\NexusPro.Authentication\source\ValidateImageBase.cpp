/*
 * ValidateImageBase.cpp - Image Base Validation
 * Original Function: _ValidateImageBase
 * Address: 0x1404DE4C0
 * 
 * Validates the image base of a loaded executable or DLL.
 * This function performs PE (Portable Executable) header validation
 * to ensure the image is properly formatted and valid.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Validate PE image base
// Original function: _ValidateImageBase
// Address: 0x1404DE4C0
__int64 ValidateImageBase(char* pImageBase) {
    __int64 result; // rax@2
    char* v2; // [sp+10h] [bp-18h]@3

    // Check for DOS header signature "MZ" (0x5A4D = 23117)
    if (*(WORD*)pImageBase == 23117) {
        // Get pointer to PE header using e_lfanew offset
        v2 = &pImageBase[*((DWORD*)pImageBase + 15)];
        
        // Check for PE signature "PE\0\0" (0x4550 = 17744)
        if (*(DWORD*)v2 == 17744) {
            // Check machine type (0x20B = 523 for x64)
            result = *((WORD*)v2 + 12) == 523;
        }
        else {
            result = 0LL;
        }
    }
    else {
        result = 0LL;
    }
    
    return result;
}
