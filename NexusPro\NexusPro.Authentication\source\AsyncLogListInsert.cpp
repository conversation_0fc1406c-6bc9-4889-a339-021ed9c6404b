/*
 * AsyncLogListInsert.cpp - Async Log List Insert Operation
 * Original Function: ?insert@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@?AV?$_Iterator@$0A@@12@V312@AEBU?$std::pair@$$CBHPEAVCAsyncLogInfo@@@2@@Z
 * Address: 0x1403C3760
 * 
 * Inserts an element into the async log list at the specified position.
 * This function adds a new key-value pair to the list and returns
 * an iterator to the inserted element.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>

// Insert element into async log list
// Original function: insert@list@pair@int@CAsyncLogInfo...
// Address: 0x1403C3760
void* AsyncLogList_insert(std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* this, void** result, void** _Where, const std::pair<int const, CAsyncLogInfo*>* _Val) {
    __int64* v4; // rdi@1
    signed __int64 i; // rcx@1
    void** v6; // rax@4
    void** v7; // rax@4
    __int64 v9; // [sp+0h] [bp-68h]@1
    char v10; // [sp+20h] [bp-48h]@4
    void** v11; // [sp+38h] [bp-30h]@4
    int v12; // [sp+40h] [bp-28h]@4
    __int64 v13; // [sp+48h] [bp-20h]@4
    void** v14; // [sp+50h] [bp-18h]@4
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v15; // [sp+70h] [bp+8h]@1
    void** v16; // [sp+78h] [bp+10h]@1
    void** __that; // [sp+80h] [bp+18h]@1
    const std::pair<int const, CAsyncLogInfo*>* v18; // [sp+88h] [bp+20h]@1

    v18 = _Val;
    __that = _Where;
    v16 = result;
    v15 = this;
    v4 = &v9;
    
    // Initialize memory with debug pattern
    for (i = 24LL; i; --i) {
        *(DWORD*)v4 = 0xCCCCCCCC;
        v4 = (__int64*)((char*)v4 + 4);
    }
    
    v13 = -2LL;
    v12 = 0;
    v11 = (void**)&v10;
    
    // Create iterator copy for insertion position
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::_Iterator<0>((void**)&v10, _Where);
    v14 = v6;
    
    // Insert element at the specified position
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Insert(v15, v6, v18);
    
    // Get iterator to the inserted element (previous position)
    v7 = std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::operator--(__that);
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::_Iterator<0>(v16, v7);
    v12 |= 1u;
    
    // Cleanup temporary iterator
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(__that);
    
    return v16;
}
