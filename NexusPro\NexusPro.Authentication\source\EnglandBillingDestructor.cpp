/*
 * EnglandBillingDestructor.cpp - England Billing Manager Destructor
 * Original Function: _CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor$0
 * Address: 0x140319C50
 * 
 * Destructor function for CEnglandBillingMgr authentication call.
 * This function handles cleanup of memory allocated during the
 * England billing authentication process.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Destructor for England billing manager authentication call
// Original function: _CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor$0
// Address: 0x140319C50
void CEnglandBillingMgr::CallFunc_RFOnline_Auth_::_1_::dtor_0(__int64 a1, __int64 a2) {
    // Delete allocated memory at offset 176
    operator delete(*(void**)(a2 + 176));
}
