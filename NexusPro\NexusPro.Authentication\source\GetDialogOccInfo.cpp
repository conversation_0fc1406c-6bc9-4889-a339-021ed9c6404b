/*
 * GetDialogOccInfo.cpp - Dialog OCC Information Retrieval
 * Original Function: ?GetOccDialogInfo@CDialog@@PEAU_AFX_OCC_DIALOG_INFO@@XZ_0
 * Address: 0x1404DBD48
 * 
 * Retrieves OLE Control Container (OCC) dialog information for CDialog class.
 * This function is part of the MFC dialog management system for handling
 * OLE controls embedded within dialog boxes.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Get OCC dialog information for CDialog class
// Returns pointer to AFX_OCC_DIALOG_INFO structure containing OLE control information
struct _AFX_OCC_DIALOG_INFO* CDialog::GetOccDialogInfo(CDialog* this) {
    return CDialog::GetOccDialogInfo(this);
}
