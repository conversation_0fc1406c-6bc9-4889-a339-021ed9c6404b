/*
 * AsyncLogListSize.cpp - Async Log List Size Operation
 * Original Function: ?size@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBA_KXZ
 * Address: 0x1403C4650
 * 
 * Returns the number of elements in the async log list.
 * This function provides the current size of the list containing
 * async log information key-value pairs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <list>
#include <utility>

// Get size of async log list
// Original function: size@list@pair@int@CAsyncLogInfo...
// Address: 0x1403C4650
unsigned __int64 AsyncLogList_size(std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* this) {
    return this->_Mysize;
}
