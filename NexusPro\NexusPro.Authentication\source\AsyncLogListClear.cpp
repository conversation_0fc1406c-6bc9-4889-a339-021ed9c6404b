/*
 * AsyncLogListClear.cpp - Async Log List Clear Operation
 * Original Function: ?clear@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@XXZ
 * Address: 0x1403C6250
 * 
 * Clears all elements from the async log list.
 * This function removes all elements from the list and deallocates
 * the memory used by the nodes, leaving the list empty.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <list>
#include <memory>
#include <utility>

// Clear all elements from async log list
// Original function: clear@list@pair@int@CAsyncLogInfo...
// Address: 0x1403C6250
void AsyncLogList_clear(std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* this, std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* a2) {
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v4; // rdx@4
    __int64 v5; // [sp+0h] [bp-38h]@1
    std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node* v6; // [sp+20h] [bp-18h]@6
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v7; // [sp+28h] [bp-10h]@4
    std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>* v8; // [sp+40h] [bp+8h]@1

    v8 = this;
    v2 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Get first node to clear
    v7 = (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Nextnode((std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v8->_Myhead, a2);
    
    // Reset head node pointers to point to itself (empty list)
    *std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Nextnode((std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v8->_Myhead, v4) = v8->_Myhead;
    *std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Prevnode((std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v8->_Myhead, v4) = v8->_Myhead;
    
    // Reset size to 0
    v8->_Mysize = 0LL;
    
    // Deallocate all nodes
    while (v7 != (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v8->_Myhead) {
        // Get next node before destroying current one
        v6 = *std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Nextnode(v7, v4);
        
        // Destroy the node's data
        std::allocator<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node>::destroy(&v8->_Alnod, (std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node*)v7);
        
        // Deallocate the node's memory
        std::allocator<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node>::deallocate(&v8->_Alnod, (std::_List_nod<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node*)v7, 1ui64);
        
        // Move to next node
        v7 = (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v6;
    }
}
