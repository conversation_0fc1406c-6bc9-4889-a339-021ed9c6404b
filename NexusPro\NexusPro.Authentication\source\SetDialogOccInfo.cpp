/*
 * SetDialogOccInfo.cpp - Dialog OCC Information Setting
 * Original Function: ?SetOccDialogInfo@CDialog@@HPEAU_AFX_OCC_DIALOG_INFO@@@Z_0
 * Address: 0x1404DBD42
 * 
 * Sets OLE Control Container (OCC) dialog information for CDialog class.
 * This function is part of the MFC dialog management system for configuring
 * OLE controls embedded within dialog boxes.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Set OCC dialog information for CDialog class
// Parameters:
//   this - Pointer to CDialog instance
//   pOccInfo - Pointer to AFX_OCC_DIALOG_INFO structure containing OLE control configuration
// Returns: Integer result code (typically success/failure status)
int CDialog::SetOccDialogInfo(CDialog* this, struct _AFX_OCC_DIALOG_INFO* pOccInfo) {
    return CDialog::SetOccDialogInfo(this, pOccInfo);
}
