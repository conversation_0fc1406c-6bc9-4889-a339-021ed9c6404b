/*
 * AsyncLogHashMapFind.cpp - Async Log Hash Map Find Operation
 * Original Function: ?find@?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@?AV?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@AEBH@Z
 * Address: 0x1403C2A70
 * 
 * Finds an element in the async log hash map by key.
 * This function searches for a specific async log entry using
 * the provided key value and returns an iterator to the found element.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <hash_map>

// Find element in async log hash map
// Original function: find@_Hash@_Hmap_traits@int@CAsyncLogInfo...
// Address: 0x1403C2A70
void* AsyncLogHashMap_find(stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* this, void** result, const int* _Keyval) {
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v6; // [sp+0h] [bp-38h]@1
    int v7; // [sp+20h] [bp-18h]@4
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* v8; // [sp+40h] [bp+8h]@1
    void** resulta; // [sp+48h] [bp+10h]@1

    resulta = result;
    v8 = this;
    v3 = &v6;
    
    // Initialize memory with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    v7 = 0;
    
    // Find element using lower_bound operation
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, stdext::hash_compare<int, std::less<int>>, std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>::lower_bound(
        v8,
        result,
        _Keyval);
    
    return resulta;
}
