/*
 * MoveMapLimitInfoDestructor0.cpp - Move Map Limit Info List Destructor 0
 * Original Function: _CMoveMapLimitInfoList::LogIn_::_1_::dtor$0
 * Address: 0x1403A5C90
 * 
 * Destructor function for CMoveMapLimitInfoList login process.
 * This function handles cleanup of vector iterators and associated
 * resources during the destruction of move map limit info objects.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <memory>

// Destructor for CMoveMapLimitInfoList login process
// Original function: _CMoveMapLimitInfoList::LogIn_::_1_::dtor$0
// Address: 0x1403A5C90
void CMoveMapLimitInfoList::LogIn_::_1_::dtor_0(__int64 a1, __int64 a2) {
    // Destroy vector iterator for CMoveMapLimitInfo pointers
    std::_Vector_iterator<CMoveMapLimitInfo*, std::allocator<CMoveMapLimitInfo*>>::~_Vector_iterator<CMoveMapLimitInfo*, std::allocator<CMoveMapLimitInfo*>>((std::_Vector_iterator<CMoveMapLimitInfo*, std::allocator<CMoveMapLimitInfo*>>*)(a2 + 144));
}
