/*
 * AsyncLogVectorEnd.cpp - Async Log Vector End Iterator
 * Original Function: ?end@?$std::vector@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@?AV?$_Vector_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@2@XZ
 * Address: 0x1403C5000
 * 
 * Returns end iterator for vector containing async log list iterators.
 * This function provides access to the past-the-end element in the vector of
 * list iterators used for async log management.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>
#include <iterator>
#include <list>
#include <utility>

// Get end iterator for async log vector
// Original function: end@vector@_Iterator@list@pair...
// Address: 0x1403C5000
void* AsyncLogVector_end(void* this, void** result) {
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-38h]@1
    int v6; // [sp+20h] [bp-18h]@4
    void** v7; // [sp+40h] [bp+8h]@1
    void** v8; // [sp+48h] [bp+10h]@1

    v8 = result;
    v7 = (void**)this;
    v2 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 12LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    v6 = 0;
    
    // Construct vector iterator pointing to past-the-end element
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, 
                         std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::
        _Vector_iterator(result, ((std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>*)this)->_Mylast);
    
    return v8;
}
