/*
 * InvalidateSky.cpp - Sky System Invalidation
 * Original Function: ?InvalidateSky@Sky@@XXZ
 * Address: 0x1405229B0
 * 
 * Invalidates sky system resources in the graphics engine.
 * This function handles cleanup of sky-related rendering resources
 * when the graphics device needs to be reset or invalidated.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Invalidate sky system resources
// Original function: ?InvalidateSky@Sky@@XXZ
// Address: 0x1405229B0
void Sky::InvalidateSky(Sky* this) {
    Sky* v1; // rbx@1
    __int64 v2; // rcx@2
    __int64 v3; // rcx@4

    v1 = this;
    
    // Check if sky system is active (version check)
    if (*(float*)&dword_184A797B0 >= 1.0) {
        // Release first sky resource
        v2 = *((QWORD*)this + 5);
        if (v2) {
            (*(void(**)(void))(*(QWORD*)v2 + 16LL))();
            *((QWORD*)v1 + 5) = 0LL;
        }
        
        // Release second sky resource
        v3 = *((QWORD*)v1 + 6);
        if (v3) {
            (*(void(**)(void))(*(QWORD*)v3 + 16LL))();
            *((QWORD*)v1 + 6) = 0LL;
        }
    }
}
