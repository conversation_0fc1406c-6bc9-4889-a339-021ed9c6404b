/*
 * AsyncLogInfoDestructor0.cpp - Async Log Info Destructor 0
 * Original Function: _CAsyncLogInfo::_CAsyncLogInfo_::_1_::dtor$0
 * Address: 0x1403BCB50
 * 
 * Destructor function for CAsyncLogInfo destructor process.
 * This function handles cleanup of critical section objects during
 * the destruction of async log info objects.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Destructor for CAsyncLogInfo destructor process
// Original function: _CAsyncLogInfo::_CAsyncLogInfo_::_1_::dtor$0
// Address: 0x1403BCB50
void CAsyncLogInfo::_CAsyncLogInfo_::_1_::dtor_0(__int64 a1, __int64 a2) {
    // Destroy critical section at offset 112 + 40
    CNetCriticalSection::~CNetCriticalSection((void*)(*((QWORD*)a2 + 112) + 40LL));
}
