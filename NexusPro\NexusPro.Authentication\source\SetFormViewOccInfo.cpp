/*
 * SetFormViewOccInfo.cpp - FormView OCC Information Setting
 * Original Function: ?SetOccDialogInfo@CFormView@@HPEAU_AFX_OCC_DIALOG_INFO@@@Z_0
 * Address: 0x1404DC156
 * 
 * Sets OLE Control Container (OCC) dialog information for CFormView class.
 * This function is part of the MFC form view management system for configuring
 * OLE controls embedded within form view dialogs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Set OCC dialog information for CFormView class
// Parameters:
//   this - Pointer to CFormView instance
//   pOccInfo - Pointer to AFX_OCC_DIALOG_INFO structure containing OLE control configuration
// Returns: Integer result code (typically success/failure status)
int CFormView::SetOccDialogInfo(CFormView* this, struct _AFX_OCC_DIALOG_INFO* pOccInfo) {
    return CFormView::SetOccDialogInfo(this, pOccInfo);
}
