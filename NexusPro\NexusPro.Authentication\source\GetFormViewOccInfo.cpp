/*
 * GetFormViewOccInfo.cpp - FormView OCC Information Retrieval
 * Original Function: ?GetOccDialogInfo@CFormView@@PEAU_AFX_OCC_DIALOG_INFO@@XZ_0
 * Address: 0x1404DC15C
 * 
 * Retrieves OLE Control Container (OCC) dialog information for CFormView class.
 * This function is part of the MFC form view management system for handling
 * OLE controls embedded within form view dialogs.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Get OCC dialog information for CFormView class
// Returns pointer to AFX_OCC_DIALOG_INFO structure containing OLE control information
struct _AFX_OCC_DIALOG_INFO* CFormView::GetOccDialogInfo(CFormView* this) {
    return CFormView::GetOccDialogInfo(this);
}
