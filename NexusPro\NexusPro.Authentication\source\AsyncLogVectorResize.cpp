/*
 * AsyncLogVectorResize.cpp - Async Log Vector Resize Operation
 * Original Function: ?resize@?$vector@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@X_KV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@@Z
 * Address: 0x1403C3F20
 * 
 * Resizes the async log vector to the specified size.
 * This function changes the size of the vector containing list iterators,
 * either by removing elements or adding new elements with the specified value.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <vector>

// Resize async log vector
// Original function: resize@vector@_Iterator@list@pair...
// Address: 0x1403C3F20
void AsyncLogVector_resize(std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* this, unsigned __int64 _Newsize, std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* _Val) {
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-F8h]@1
    char v6; // [sp+20h] [bp-D8h]@5
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* result; // [sp+38h] [bp-C0h]@5
    char v8; // [sp+40h] [bp-B8h]@7
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* v9; // [sp+58h] [bp-A0h]@7
    char v10; // [sp+60h] [bp-98h]@7
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* v11; // [sp+78h] [bp-80h]@7
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>> v12; // [sp+80h] [bp-78h]@7
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>> v13; // [sp+98h] [bp-60h]@7
    __int64 v14; // [sp+B0h] [bp-48h]@4
    unsigned __int64 v15; // [sp+B8h] [bp-40h]@5
    std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* v16; // [sp+C0h] [bp-38h]@5
    std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>* v22; // [sp+100h] [bp+8h]@1
    unsigned __int64 _Off; // [sp+108h] [bp+10h]@1
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* v24; // [sp+110h] [bp+18h]@1

    v24 = _Val;
    _Off = _Newsize;
    v22 = this;
    v3 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 60LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    v14 = -2LL;
    
    // Check if current size is greater than or equal to new size
    if (std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::size(v22) >= _Newsize) {
        // Shrink vector if new size is smaller
        if (_Off < std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::size(v22)) {
            v9 = (std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>*)&v8;
            v11 = (std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>*)&v10;
            
            // Get end iterator
            auto v17 = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::end(v22, (std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>*)&v8);
            
            // Get begin iterator
            auto v19 = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::begin(v22, &v12);
            
            // Calculate position to erase from
            auto v21 = std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::operator+(v19, v11, _Off);
            
            // Erase elements from new size to end
            std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::erase(v22, &v13, v21, v17);
            
            // Cleanup iterators
            std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::~_Vector_iterator(&v13);
            std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::~_Vector_iterator(&v12);
        }
    }
    else {
        // Expand vector if new size is larger
        result = (std::_Vector_iterator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>*)&v6;
        
        // Calculate number of elements to add
        v15 = _Off - std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::size(v22);
        
        // Get end iterator
        v16 = std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::end(v22, result);
        
        // Insert new elements at the end
        std::vector<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>, std::allocator<std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>>>::_Insert_n(v22, v16, v15, v24);
    }
    
    // Cleanup value iterator
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>::~_Iterator<0>(v24);
}
