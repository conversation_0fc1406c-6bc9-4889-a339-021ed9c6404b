/*
 * R3InvalidateDevice.cpp - R3 Device Invalidation
 * Original Function: ?R3InvalidateDevice@@YAJXZ
 * Address: 0x1404E9FC0
 * 
 * Invalidates R3 graphics device resources.
 * This function serves as a wrapper for D3D device invalidation,
 * handling cleanup when the graphics device is lost or reset.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Invalidate R3 graphics device
// Original function: ?R3InvalidateDevice@@YAJXZ
// Address: 0x1404E9FC0
__int32 R3InvalidateDevice(void) {
    return D3D_R3InvalidateDevice();
}
