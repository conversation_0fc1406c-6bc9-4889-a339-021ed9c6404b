/*
 * AsyncLogAllocatorMaxSize.cpp - Async Log Allocator Maximum Size
 * Original Function: ?max_size@?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x1403C6F00
 * 
 * Returns the maximum number of elements that can be allocated by the
 * async log allocator for pairs containing integers and CAsyncLogInfo pointers.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <memory>
#include <utility>

// Get maximum size for async log allocator
// Original function: max_size@allocator@pair...
// Address: 0x1403C6F00
signed __int64 AsyncLogAllocator_max_size(std::allocator<std::pair<int const, CAsyncLogInfo*>>* this) {
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-18h]@1

    v1 = &v4;
    
    // Initialize memory with debug pattern
    for (i = 4LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }
    
    // Return maximum possible allocation size
    return 0xFFFFFFFFFFFFFFFi64;
}
