/*
 * AsyncLogListNodeDeallocator.cpp - Async Log List Node Deallocator
 * Original Function: ?deallocate@?$allocator@U_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@XPEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@_K@Z
 * Address: 0x1403C6780
 * 
 * Deallocates memory for list nodes in the async log system.
 * This function handles memory deallocation for STL list nodes containing
 * pairs of integers and CAsyncLogInfo pointers.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <utility>

// Deallocate memory for async log list nodes
// Original function: deallocate@allocator@U_Node@_List_nod...
// Address: 0x1403C6780
void AsyncLogListNodeDeallocator_deallocate(void* this, void* _Ptr, unsigned __int64 __formal) {
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1

    v3 = &v5;
    
    // Initialize memory with debug pattern
    for (i = 8LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    // Deallocate the memory
    operator delete(_Ptr);
}
