/*
 * MoveMapLimitInfoDestructor1.cpp - Move Map Limit Info Destructor 1
 * Original Function: _CMoveMapLimitInfoList::LogIn_::_1_::dtor$1
 * Address: 0x1403A5CC0
 * 
 * Destructor function for CMoveMapLimitInfoList login process.
 * This function handles cleanup of vector const iterators and associated
 * resources during the destruction of move map limit info objects.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <memory>

// Destructor for CMoveMapLimitInfoList login process
// Original function: _CMoveMapLimitInfoList::LogIn_::_1_::dtor$1
// Address: 0x1403A5CC0
void CMoveMapLimitInfoList::LogIn_::_1_::dtor_1(__int64 a1, __int64 a2) {
    // Destroy vector const iterator for CMoveMapLimitInfo pointers
    std::_Vector_const_iterator<CMoveMapLimitInfo*, std::allocator<CMoveMapLimitInfo*>>::~_Vector_const_iterator<CMoveMapLimitInfo*, std::allocator<CMoveMapLimitInfo*>>((std::_Vector_const_iterator<CMoveMapLimitInfo*, std::allocator<CMoveMapLimitInfo*>>*)(a2 + 56));
}
