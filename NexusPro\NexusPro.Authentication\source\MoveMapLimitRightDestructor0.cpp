/*
 * MoveMapLimitRightDestructor0.cpp - Move Map Limit Right Info Destructor 0
 * Original Function: _CMoveMapLimitRightInfo::LogIn_::_1_::dtor$0
 * Address: 0x1403AD090
 * 
 * Destructor function for CMoveMapLimitRightInfo login process.
 * This function handles cleanup of vector iterators and associated
 * resources during the destruction of move map limit right info objects.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <memory>

// Destructor for CMoveMapLimitRightInfo login process
// Original function: _CMoveMapLimitRightInfo::LogIn_::_1_::dtor$0
// Address: 0x1403AD090
void CMoveMapLimitRightInfo::LogIn_::_1_::dtor_0(__int64 a1, __int64 a2) {
    // Destroy vector iterator for CMoveMapLimitRight pointers
    std::_Vector_iterator<CMoveMapLimitRight*, std::allocator<CMoveMapLimitRight*>>::~_Vector_iterator<CMoveMapLimitRight*, std::allocator<CMoveMapLimitRight*>>((std::_Vector_iterator<CMoveMapLimitRight*, std::allocator<CMoveMapLimitRight*>>*)(a2 + 144));
}
