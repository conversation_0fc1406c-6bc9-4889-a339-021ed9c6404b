/*
 * HMACMessageAuthCodeVectorDestructor.cpp - HMAC Message Authentication Code Vector Destructor
 * Original Function: ??_E?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@W7EAAPEAXI@Z
 * Address: 0x14046AC80
 * 
 * Vector deleting destructor for HMAC-based message authentication code implementation.
 * This function handles proper cleanup of HMAC SHA1 message authentication code objects
 * when they are stored in vectors or arrays.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Vector deleting destructor for HMAC message authentication code
// Original function: vector deleting destructor for MessageAuthenticationCodeImpl@HMAC_Base@HMAC@SHA1...
// Address: 0x14046AC80
void* CryptoPP_MessageAuthenticationCodeImpl_HMAC_SHA1_VectorDeletingDestructor(__int64 a1, unsigned int a2) {
    // Call the actual vector deleting destructor with adjusted pointer
    // The pointer is adjusted by -8 to account for virtual table offset
    return CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>::vector_deleting_destructor(
        (CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>*)(a1 - 8),
        a2);
}
