/*
 * GuildBattleGuildLogin.cpp - Guild Battle Guild Login Handler
 * Original Function: ?LogIn@CNormalGuildBattleGuild@GUILD_BATTLE@@XHKEPEADIPEAVCNormalGuildBattleField@2@AEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E0DD0
 * 
 * Handles login process for guild battle guild members.
 * This function manages member authentication, position notification,
 * and field assignment during guild battle sessions.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Handle guild battle guild login process
// Original function: ?LogIn@CNormalGuildBattleGuild@GUILD_BATTLE@@XHKEPEADIPEAVCNormalGuildBattleField@2@AEAVCNormalGuildBattleLogger@2@@Z
// Address: 0x1403E0DD0
void GUILD_BATTLE::CNormalGuildBattleGuild::LogIn(GUILD_BATTLE::CNormalGuildBattleGuild* this, int n, unsigned int dwSerial, char GuildBattleNumber, char* wszDestGuild, unsigned int uiID, GUILD_BATTLE::CNormalGuildBattleField* pkField, GUILD_BATTLE::CNormalGuildBattleLogger* kLogger) {
    __int64* v8; // rdi@1
    signed __int64 i; // rcx@1
    CPlayer* v10; // rax@8
    CPlayer* v11; // rax@8
    __int64 v12; // [sp+0h] [bp-58h]@1
    GUILD_BATTLE::CNormalGuildBattleLogger* v13; // [sp+20h] [bp-38h]@6
    char* v14; // [sp+28h] [bp-30h]@6
    unsigned int v15; // [sp+30h] [bp-28h]@6
    int iMember; // [sp+40h] [bp-18h]@4
    GUILD_BATTLE::CNormalGuildBattleGuild* v17; // [sp+60h] [bp+8h]@1
    int na; // [sp+68h] [bp+10h]@1
    unsigned int dwSeriala; // [sp+70h] [bp+18h]@1
    char v20; // [sp+78h] [bp+20h]@1

    v20 = GuildBattleNumber;
    dwSeriala = dwSerial;
    na = n;
    v17 = this;
    v8 = &v12;
    
    // Initialize memory with debug pattern
    for (i = 20LL; i; --i) {
        *(DWORD*)v8 = 0xCCCCCCCC;
        v8 = (__int64*)((char*)v8 + 4);
    }
    
    // Get member index by serial number
    iMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(v17, dwSerial);
    
    // Check if member exists
    if (iMember >= 0) {
        // Get player and send position notifications
        v10 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v17->m_kMember[iMember]);
        GUILD_BATTLE::CNormalGuildBattleGuild::SendOhterNotifyCommitteeMemberPosition(v17, v10);
        
        v11 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v17->m_kMember[iMember]);
        GUILD_BATTLE::CNormalGuildBattleGuild::SendSelfNotifyCommitteeMemberPositionList(v17, v11);
        
        // Move member to battle field
        GUILD_BATTLE::CNormalGuildBattleGuild::MoveMember(v17, iMember, uiID, pkField, kLogger);
        
        v15 = uiID;
        v14 = wszDestGuild;
        v13 = (GUILD_BATTLE::CNormalGuildBattleLogger*)(unsigned __int8)v20;
        
        // Log successful login
        GUILD_BATTLE::CNormalGuildBattleLogger::Log(
            kLogger,
            "CNormalGuildBattleGuild::LogIn( n(%d), dwSerial(%u), GuildBattleNumber(%u), %s, uiID(%u), pkField)",
            (unsigned int)na,
            dwSeriala);
    }
    else if ((unsigned __int8)v20 > v17->m_dwCurJoinMember) {
        // Ask to join if member not found but has capacity
        GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(v17, na, wszDestGuild, kLogger);
        
        v15 = uiID;
        v14 = wszDestGuild;
        v13 = (GUILD_BATTLE::CNormalGuildBattleLogger*)(unsigned __int8)v20;
        
        // Log join request
        GUILD_BATTLE::CNormalGuildBattleLogger::Log(
            kLogger,
            "CNormalGuildBattleGuild::LogIn( n(%d), dwSerial(%u), GuildBattleNumber(%u), %s, uiID(%u), pkField) : Ask Join",
            (unsigned int)na,
            dwSeriala);
    }
}
