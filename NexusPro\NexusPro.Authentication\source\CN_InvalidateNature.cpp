/*
 * CN_InvalidateNature.cpp - Nature System Invalidation
 * Original Function: ?CN_InvalidateNature@@YAXXZ
 * Address: 0x140504ED0
 * 
 * Invalidates nature rendering system components.
 * This function handles cleanup of sky and sun rendering systems
 * when the graphics device needs to be reset or invalidated.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Invalidate nature rendering system
// Original function: ?CN_InvalidateNature@@YAXXZ
// Address: 0x140504ED0
void CN_InvalidateNature(void) {
    // Invalidate sky rendering system
    Sky::InvalidateSky((Sky*)&unk_184A79E58);
    
    // Invalidate sun rendering system
    Sun::InvalidateSun((Sun*)&dword_184A79E28);
}
