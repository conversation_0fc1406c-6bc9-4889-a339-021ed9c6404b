/*
 * InvalidateR3FontDevice.cpp - R3Font Device Objects Invalidation
 * Original Function: ?InvalidateDeviceObjects@CR3Font@@JXZ
 * Address: 0x140528820
 * 
 * Invalidates device objects for CR3Font class in the graphics system.
 * This function handles cleanup of font-related device resources when
 * the graphics device is lost or needs to be reset.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Invalidate device objects for CR3Font class
// Original function: ?InvalidateDeviceObjects@CR3Font@@JXZ
// Address: 0x140528820
__int64 CR3Font::InvalidateDeviceObjects(CR3Font* this) {
    CR3Font* v1; // rbx@1
    __int64 v2; // rcx@1

    v1 = this;
    v2 = *(QWORD*)this;
    
    // Check if device object exists
    if (v2) {
        // Release first device object if valid
        if (*((DWORD*)v1 + 25)) {
            (*(void(**)(void))(*(QWORD*)v2 + 448LL))();
        }
        
        // Release second device object if valid
        if (*((DWORD*)v1 + 26)) {
            (*(void(**)(void))(**(QWORD**)v1 + 448LL))();
        }
    }
    
    // Clear device object references
    *((DWORD*)v1 + 25) = 0;
    *((DWORD*)v1 + 26) = 0;
    
    // Perform private release cleanup
    CR3Font::PrivateRelease(v1);
    return 0LL;
}
